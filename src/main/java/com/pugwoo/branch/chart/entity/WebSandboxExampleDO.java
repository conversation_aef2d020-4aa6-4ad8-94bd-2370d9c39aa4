package com.pugwoo.branch.chart.entity;

import com.pugwoo.admin.bean.AdminCoreDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("web_sandbox_example")
public class WebSandboxExampleDO extends AdminCoreDO {

    /** example名称<br/>Column: [name] */
    @Column(value = "name")
    private String name;

    /** chart的库<br/>Column: [library] */
    @Column(value = "library")
    private String library;

    /** chart的图表名称<br/>Column: [charts] */
    @Column(value = "charts")
    private String charts;

    /** 创建图表的完整请求体<br/>Column: [req_body] */
    @Column(value = "req_body")
    private String reqBody;

    /** sandbox链接code<br/>Column: [link_code] */
    @Column(value = "link_code")
    private String linkCode;

    /** 描述<br/>Column: [description] */
    @Column(value = "description")
    private String description;

    /** 排序，从小到大排列<br/>Column: [seq] */
    @Column(value = "seq")
    private Integer seq;

}