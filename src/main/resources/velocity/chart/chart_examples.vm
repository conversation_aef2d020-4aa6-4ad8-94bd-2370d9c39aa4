#set($page_title='图表示例管理')

<style>
    #app {
        margin: 0;
    }
    .example-item {
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        margin-bottom: 20px;
        padding: 15px;
    }
    .example-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        padding-bottom: 10px;
        border-bottom: 1px solid #f0f0f0;
    }
    .example-content {
        display: flex;
        gap: 20px;
        height: 400px;
    }
    .req-body-section {
        flex: 1;
        display: flex;
        flex-direction: column;
    }
    .chart-section {
        flex: 1;
        border: 1px solid #ddd;
        border-radius: 4px;
    }
    .chart-iframe {
        width: 100%;
        height: 100%;
        border: none;
        border-radius: 4px;
    }
    .json-editor {
        height: 350px;
        font-family: 'Courier New', monospace;
        font-size: 12px;
    }
    .json-editor .el-textarea__inner {
        height: 100%;
    }
    .library-charts-selector {
        margin-bottom: 20px;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 4px;
    }
    .create-example-form {
        margin-top: 20px;
        padding: 15px;
        background-color: #f0f9ff;
        border-radius: 4px;
        border: 1px solid #e1f5fe;
    }
</style>

<div id="app" v-cloak>
    <!-- Library和Charts选择器 -->
    <div class="library-charts-selector">
        <el-form :inline="true">
            <el-form-item label="图表库">
                <el-select v-model="selectedLibrary" placeholder="请选择图表库" @change="onLibraryChange" style="width: 200px;">
                    <el-option v-for="library in libraries" :key="library" :label="library" :value="library"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="图表类型">
                <el-select v-model="selectedCharts" placeholder="请选择图表类型" @change="onChartsChange" style="width: 200px;">
                    <el-option v-for="chart in currentCharts" :key="chart" :label="chart" :value="chart"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="success" @click="showCreateForm = !showCreateForm">{{ showCreateForm ? '取消创建' : '创建新示例' }}</el-button>
            </el-form-item>
        </el-form>
    </div>

    <!-- 创建新示例表单 -->
    <div v-show="showCreateForm" class="create-example-form">
        <h4>创建新示例</h4>
        <el-form :model="createForm" label-width="100px">
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="示例名称" required>
                        <el-input v-model="createForm.name" placeholder="请输入示例名称"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="描述">
                        <el-input v-model="createForm.description" placeholder="请输入描述"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-form-item label="请求体" required>
                <el-input v-model="createForm.reqBody" type="textarea" :rows="8" placeholder="请输入JSON格式的请求体"></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="createExample">创建示例</el-button>
                <el-button @click="resetCreateForm">重置</el-button>
            </el-form-item>
        </el-form>
    </div>

    <!-- 示例列表 -->
    <div v-if="examples.length > 0">
        <h3>{{ selectedLibrary }} - {{ selectedCharts }} 示例列表</h3>
        <div v-for="(example, index) in examples" :key="example.id" class="example-item">
            <div class="example-header">
                <div>
                    <strong>{{ example.name }}</strong>
                    <span v-if="example.description" style="color: #666; margin-left: 10px;">{{ example.description }}</span>
                </div>
                <div>
                    <el-button size="small" @click="editExample(example)">编辑</el-button>
                    <el-button size="small" type="danger" @click="deleteExample(example)">删除</el-button>
                </div>
            </div>
            <div class="example-content">
                <div class="req-body-section">
                    <div style="margin-bottom: 10px;">
                        <strong>请求体：</strong>
                        <el-button size="mini" @click="generateTempLink(index)" style="margin-left: 10px;">生成临时链接</el-button>
                        <el-button size="mini" type="primary" @click="saveExample(index)" style="margin-left: 5px;">保存并生成永久链接</el-button>
                    </div>
                    <el-input 
                        v-model="example.reqBody" 
                        type="textarea" 
                        class="json-editor"
                        @input="onReqBodyChange(index)"
                        placeholder="JSON格式的请求体">
                    </el-input>
                </div>
                <div class="chart-section">
                    <iframe 
                        v-if="example.currentLinkCode" 
                        :src="getChartUrl(example.currentLinkCode)" 
                        class="chart-iframe">
                    </iframe>
                    <div v-else style="display: flex; align-items: center; justify-content: center; height: 350px; color: #999;">
                        请编辑请求体并生成链接以预览图表
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div v-else-if="selectedLibrary && selectedCharts" style="text-align: center; padding: 50px; color: #999;">
        暂无示例，请创建新示例
    </div>

    <!-- 编辑示例对话框 -->
    <el-dialog title="编辑示例" :visible.sync="showEditDialog" width="60%">
        <el-form :model="editForm" label-width="100px">
            <el-form-item label="示例名称" required>
                <el-input v-model="editForm.name"></el-input>
            </el-form-item>
            <el-form-item label="描述">
                <el-input v-model="editForm.description"></el-input>
            </el-form-item>
            <el-form-item label="请求体" required>
                <el-input v-model="editForm.reqBody" type="textarea" :rows="10"></el-input>
            </el-form-item>
        </el-form>
        <div slot="footer">
            <el-button @click="showEditDialog = false">取消</el-button>
            <el-button type="primary" @click="updateExample">确定</el-button>
        </div>
    </el-dialog>
</div>

<script>
var vm = new Vue({
    el: '#app',
    data: {
        // 基础数据
        libraries: [],
        libraryChartsMap: {},
        selectedLibrary: '',
        selectedCharts: '',
        currentCharts: [],
        examples: [],
        
        // 表单数据
        showCreateForm: false,
        createForm: {
            name: '',
            description: '',
            reqBody: ''
        },
        
        // 编辑对话框
        showEditDialog: false,
        editForm: {
            id: null,
            name: '',
            description: '',
            reqBody: ''
        }
    },
    created: function() {
        this.loadLibraryCharts()
    },
    methods: {
        // 加载library和charts数据
        loadLibraryCharts: function() {
            var that = this
            Resource.get("${_contextPath_}/chart_examples/library_charts", {}, function(resp) {
                that.libraries = resp.data.libraries
                that.libraryChartsMap = resp.data.libraryChartsMap

                // 自动选中第一个图表库和第一个图表类型
                if (that.libraries.length > 0) {
                    that.selectedLibrary = that.libraries[0]
                    that.onLibraryChange()

                    // 选中第一个图表类型
                    if (that.currentCharts.length > 0) {
                        that.selectedCharts = that.currentCharts[0]
                        that.onChartsChange()
                    }
                }
            })
        },
        
        // library选择变化
        onLibraryChange: function() {
            this.currentCharts = this.libraryChartsMap[this.selectedLibrary] || []
            this.selectedCharts = ''
            this.examples = []
        },
        
        // charts选择变化
        onChartsChange: function() {
            this.examples = []
            // 自动加载示例
            this.loadExamples()
        },
        
        // 加载示例
        loadExamples: function() {
            if (!this.selectedLibrary || !this.selectedCharts) {
                return
            }
            var that = this
            Resource.get("${_contextPath_}/chart_examples/examples", {
                library: this.selectedLibrary,
                charts: this.selectedCharts
            }, function(resp) {
                that.examples = resp.data.map(function(item) {
                    return {
                        ...item,
                        currentLinkCode: item.linkCode,
                        originalReqBody: item.reqBody
                    }
                })
            })
        },
        
        // 请求体变化
        onReqBodyChange: function(index) {
            // 清除当前的linkCode，表示需要重新生成
            this.examples[index].currentLinkCode = ''
        },
        
        // 生成临时链接
        generateTempLink: function(index) {
            var that = this
            var example = this.examples[index]
            if (!example.reqBody.trim()) {
                Message.error("请求体不能为空")
                return
            }
            
            Resource.post("${_contextPath_}/chart_examples/generate_link_code", {
                reqBody: example.reqBody,
                temporary: true
            }, function(resp) {
                that.$set(that.examples, index, {
                    ...example,
                    currentLinkCode: resp.data.linkCode
                })
                Message.success("临时链接生成成功")
            })
        },
        
        // 保存示例并生成永久链接
        saveExample: function(index) {
            var that = this
            var example = this.examples[index]
            if (!example.reqBody.trim()) {
                Message.error("请求体不能为空")
                return
            }
            
            Resource.post("${_contextPath_}/chart_examples/update", {
                id: example.id,
                reqBody: example.reqBody
            }, function(resp) {
                that.$set(that.examples, index, {
                    ...example,
                    linkCode: resp.data.linkCode,
                    currentLinkCode: resp.data.linkCode,
                    originalReqBody: example.reqBody
                })
                Message.success("保存成功，永久链接已生成")
            })
        },
        
        // 创建示例
        createExample: function() {
            if (!this.createForm.name.trim()) {
                Message.error("示例名称不能为空")
                return
            }
            if (!this.createForm.reqBody.trim()) {
                Message.error("请求体不能为空")
                return
            }
            
            var that = this
            Resource.postJson("${_contextPath_}/chart_examples/create", {
                name: this.createForm.name,
                library: this.selectedLibrary,
                charts: this.selectedCharts,
                reqBody: this.createForm.reqBody,
                description: this.createForm.description
            }, function(resp) {
                Message.success("创建成功")
                that.resetCreateForm()
                that.showCreateForm = false
                that.loadExamples()
            })
        },
        
        // 重置创建表单
        resetCreateForm: function() {
            this.createForm = {
                name: '',
                description: '',
                reqBody: ''
            }
        },
        
        // 编辑示例
        editExample: function(example) {
            this.editForm = {
                id: example.id,
                name: example.name,
                description: example.description || '',
                reqBody: example.reqBody
            }
            this.showEditDialog = true
        },
        
        // 更新示例
        updateExample: function() {
            if (!this.editForm.name.trim()) {
                Message.error("示例名称不能为空")
                return
            }
            if (!this.editForm.reqBody.trim()) {
                Message.error("请求体不能为空")
                return
            }
            
            var that = this
            Resource.post("${_contextPath_}/chart_examples/update", this.editForm, function(resp) {
                Message.success("更新成功")
                that.showEditDialog = false
                that.loadExamples()
            })
        },
        
        // 删除示例
        deleteExample: function(example) {
            var that = this
            Message.confirm("确定要删除示例 '" + example.name + "' 吗？", function() {
                Resource.post("${_contextPath_}/chart_examples/delete", {id: example.id}, function() {
                    Message.success("删除成功")
                    that.loadExamples()
                })
            })
        },
        
        // 获取图表URL
        getChartUrl: function(linkCode) {
            return "${_contextPath_}/chart/" + linkCode
        }
    }
})
</script>
